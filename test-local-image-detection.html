<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地图片路径检测测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-content {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>本地图片路径检测功能测试</h1>
    
    <div class="instructions">
        <h3>测试说明：</h3>
        <p>1. 复制下面的测试内容到 Vditor 编辑器中</p>
        <p>2. 观察是否出现警告提醒</p>
        <p>3. 检查控制台是否有相关日志输出</p>
    </div>

    <div class="test-section">
        <h3>测试用例 1：包含本地文件路径的内容</h3>
        <div class="test-content">这是一段包含本地图片的文本：

![测试图片1](file:///C:/Users/<USER>/AppData/Local/Temp/ksohtml10740/wps17.jpg)

这里还有一些文字。

![另一张图片](file:///D:/Documents/Pictures/screenshot.png)

更多内容...</div>
    </div>

    <div class="test-section">
        <h3>测试用例 2：包含网络图片的内容（不应触发警告）</h3>
        <div class="test-content">这是一段包含网络图片的文本：

![网络图片](https://example.com/image.jpg)

这里还有一些文字。

![另一张网络图片](https://cdn.example.com/photo.png)

更多内容...</div>
    </div>

    <div class="test-section">
        <h3>测试用例 3：混合内容</h3>
        <div class="test-content">这是一段混合内容：

![网络图片](https://example.com/image.jpg)
![本地图片](file:///C:/Users/<USER>/Desktop/local-image.jpg)
![另一张网络图片](https://cdn.example.com/photo.png)
![另一张本地图片](file:///E:/Photos/vacation.jpg)

更多内容...</div>
    </div>

    <div class="test-section">
        <h3>测试用例 4：无图片名称的本地路径</h3>
        <div class="test-content">![](file:///C:/Users/<USER>/AppData/Local/Temp/ksohtml10740/wps17.jpg)

![](file:///D:/Documents/Pictures/screenshot.png)</div>
    </div>

    <script>
        // 提供复制功能
        document.querySelectorAll('.test-content').forEach(element => {
            element.addEventListener('click', function() {
                navigator.clipboard.writeText(this.textContent).then(() => {
                    const originalBg = this.style.backgroundColor;
                    this.style.backgroundColor = '#c8e6c9';
                    setTimeout(() => {
                        this.style.backgroundColor = originalBg;
                    }, 500);
                });
            });
        });
    </script>
</body>
</html>
